<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Item Story Keeper - Prototype Gallery</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #FFFDF7 0%, #FFF9F0 100%);
            padding: 20px;
            min-height: 100vh;
        }

        .gallery-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .gallery-title {
            font-size: 32px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 8px;
        }

        .gallery-subtitle {
            font-size: 18px;
            color: #666666;
            margin-bottom: 20px;
        }

        .brand-colors {
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .color-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #EFEFEF;
        }

        .color-primary { background: #FF8FA3; }
        .color-secondary { background: #89CFF0; }

        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .prototype-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 1px solid #EFEFEF;
        }

        .prototype-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .prototype-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #FF8FA3, #89CFF0);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 12px;
        }

        .prototype-title {
            font-size: 18px;
            font-weight: 600;
            color: #333333;
        }

        .prototype-description {
            font-size: 14px;
            color: #666666;
            margin-bottom: 16px;
            line-height: 1.4;
        }

        .phone-frame {
            width: 100%;
            max-width: 393px;
            margin: 0 auto;
            background: #000;
            border-radius: 24px;
            padding: 4px;
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 852px;
            border-radius: 20px;
            overflow: hidden;
            background: white;
            position: relative;
        }

        .landscape-frame {
            width: 100%;
            max-width: 852px;
            height: 393px;
            margin: 0 auto;
            background: #000;
            border-radius: 24px;
            padding: 4px;
        }

        .landscape-screen {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            overflow: hidden;
            background: white;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 20px;
        }

        .orientation-label {
            position: absolute;
            top: -30px;
            right: 0;
            background: #FF8FA3;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="gallery-header">
        <h1 class="gallery-title">Item Story Keeper</h1>
        <p class="gallery-subtitle">High-Fidelity Prototype Gallery</p>
        <div class="brand-colors">
            <div class="color-dot color-primary"></div>
            <div class="color-dot color-secondary"></div>
        </div>
        <p style="color: #666; font-size: 14px;">Emotional memories of items • Interactive storytelling • Social sharing</p>
    </div>

    <div class="prototype-grid">
        <!-- Portrait Orientation Pages -->
        <div class="prototype-card">
            <div class="prototype-header">
                <div class="prototype-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div>
                    <div class="prototype-title">Home Dashboard</div>
                </div>
            </div>
            <p class="prototype-description">Main item stream with card layout, featuring recent stories and quick actions</p>
            <div class="phone-frame">
                <div class="orientation-label">Portrait</div>
                <div class="status-indicator"></div>
                <div class="phone-screen">
                    <iframe src="pages/home.html"></iframe>
                </div>
            </div>
        </div>

        <div class="prototype-card">
            <div class="prototype-header">
                <div class="prototype-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div>
                    <div class="prototype-title">Story Collections</div>
                </div>
            </div>
            <p class="prototype-description">Themed collections of items with play-all functionality and celebration animations</p>
            <div class="phone-frame">
                <div class="orientation-label">Portrait</div>
                <div class="status-indicator"></div>
                <div class="phone-screen">
                    <iframe src="pages/collections.html"></iframe>
                </div>
            </div>
        </div>

        <div class="prototype-card">
            <div class="prototype-header">
                <div class="prototype-icon">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div>
                    <div class="prototype-title">Add New Item</div>
                </div>
            </div>
            <p class="prototype-description">Conversational recording flow with AI voice generation and story input</p>
            <div class="phone-frame">
                <div class="orientation-label">Portrait</div>
                <div class="status-indicator"></div>
                <div class="phone-screen">
                    <iframe src="pages/add-item.html"></iframe>
                </div>
            </div>
        </div>

        <div class="prototype-card">
            <div class="prototype-header">
                <div class="prototype-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div>
                    <div class="prototype-title">Item Detail</div>
                </div>
            </div>
            <p class="prototype-description">Individual story view with voice playback, editing options, and sharing features</p>
            <div class="phone-frame">
                <div class="orientation-label">Portrait</div>
                <div class="status-indicator"></div>
                <div class="phone-screen">
                    <iframe src="pages/item-detail.html"></iframe>
                </div>
            </div>
        </div>

        <div class="prototype-card">
            <div class="prototype-header">
                <div class="prototype-icon">
                    <i class="fas fa-calendar-star"></i>
                </div>
                <div>
                    <div class="prototype-title">Memories & Anniversaries</div>
                </div>
            </div>
            <p class="prototype-description">Anniversary timeline with celebration animations and commemorative stickers</p>
            <div class="phone-frame">
                <div class="orientation-label">Portrait</div>
                <div class="status-indicator"></div>
                <div class="phone-screen">
                    <iframe src="pages/memories.html"></iframe>
                </div>
            </div>
        </div>

        <div class="prototype-card">
            <div class="prototype-header">
                <div class="prototype-icon">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div>
                    <div class="prototype-title">Profile & Settings</div>
                </div>
            </div>
            <p class="prototype-description">Account management, subscription options, and app preferences</p>
            <div class="phone-frame">
                <div class="orientation-label">Portrait</div>
                <div class="status-indicator"></div>
                <div class="phone-screen">
                    <iframe src="pages/profile.html"></iframe>
                </div>
            </div>
        </div>

        <!-- Landscape Orientation Pages -->
        <div class="prototype-card">
            <div class="prototype-header">
                <div class="prototype-icon">
                    <i class="fas fa-image"></i>
                </div>
                <div>
                    <div class="prototype-title">Postcard Creator</div>
                </div>
            </div>
            <p class="prototype-description">Landscape editing interface with drag-drop items, custom backgrounds, and decorative elements</p>
            <div class="landscape-frame">
                <div class="orientation-label">Landscape</div>
                <div class="status-indicator"></div>
                <div class="landscape-screen">
                    <iframe src="pages/postcard-create.html"></iframe>
                </div>
            </div>
        </div>

        <div class="prototype-card">
            <div class="prototype-header">
                <div class="prototype-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <div>
                    <div class="prototype-title">Scene Collage</div>
                </div>
            </div>
            <p class="prototype-description">Interactive scene templates with hand-drawn style and item placement animations</p>
            <div class="landscape-frame">
                <div class="orientation-label">Landscape</div>
                <div class="status-indicator"></div>
                <div class="landscape-screen">
                    <iframe src="pages/scene-collage.html"></iframe>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
