<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home - Item Story Keeper</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared.css">
    <style>
        .home-header {
            padding: 20px;
            background: linear-gradient(135deg, rgba(255, 143, 163, 0.1) 0%, rgba(137, 207, 240, 0.1) 100%);
            position: relative;
            overflow: hidden;
        }

        .home-header::before {
            content: '';
            position: absolute;
            top: -50px;
            right: -50px;
            width: 100px;
            height: 100px;
            background: rgba(255, 143, 163, 0.1);
            border-radius: 50%;
        }

        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 16px;
            color: #666666;
            margin-bottom: 20px;
        }

        .quick-stats {
            display: flex;
            gap: 16px;
        }

        .stat-item {
            flex: 1;
            background: white;
            padding: 12px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 600;
            color: #FF8FA3;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666666;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 12px 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333333;
        }

        .see-all {
            font-size: 14px;
            color: #FF8FA3;
            text-decoration: none;
        }

        .item-stream {
            padding: 0 20px 20px 20px;
        }

        .item-card {
            background: white;
            border-radius: 16px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #EFEFEF;
            position: relative;
            transition: all 0.3s ease;
        }

        .item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .item-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
        }

        .item-content {
            padding: 16px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .item-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 4px;
        }

        .item-date {
            font-size: 12px;
            color: #999999;
        }

        .item-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            border: none;
            background: #F8F9FA;
            color: #666666;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #FF8FA3;
            color: white;
            transform: scale(1.1);
        }

        .item-story {
            font-size: 14px;
            color: #666666;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .item-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .tag {
            background: rgba(255, 143, 163, 0.1);
            color: #FF8FA3;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .item-footer {
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .play-story {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #89CFF0;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 8px;
            background: rgba(137, 207, 240, 0.1);
            transition: all 0.3s ease;
        }

        .play-story:hover {
            background: #89CFF0;
            color: white;
        }

        .floating-add {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #FF8FA3, #FF7A96);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(255, 143, 163, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .floating-add:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(255, 143, 163, 0.5);
        }

        .decorative-element {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 20px;
            height: 20px;
            background: rgba(137, 207, 240, 0.3);
            border-radius: 50%;
            animation: pulse 3s infinite;
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-left">9:41</div>
        <div class="status-right">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <div class="battery-icon">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Home Header -->
        <div class="home-header">
            <div class="decorative-element"></div>
            <div class="greeting">Good morning, Sarah ✨</div>
            <div class="subtitle">Your stories are waiting to be cherished</div>
            
            <div class="quick-stats">
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Items</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Collections</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">Anniversaries</div>
                </div>
            </div>
        </div>

        <!-- Recent Stories Section -->
        <div class="section-header">
            <div class="section-title">Recent Stories</div>
            <a href="#" class="see-all">See all</a>
        </div>

        <div class="item-stream">
            <!-- Item Card 1 -->
            <div class="item-card animate-fade-in-up">
                <img src="https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=200&fit=crop" alt="Vintage Camera" class="item-image">
                <div class="item-content">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Grandpa's Camera</div>
                            <div class="item-date">Added 2 days ago</div>
                        </div>
                        <div class="item-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="item-tags">
                        <span class="tag">Family</span>
                        <span class="tag">Vintage</span>
                    </div>
                    
                    <div class="item-story">
                        This old camera belonged to my grandfather. He used it to capture countless family moments throughout the 1960s and 70s. Every click tells a story of love and memories...
                    </div>
                    
                    <div class="item-footer">
                        <a href="#" class="play-story">
                            <i class="fas fa-play"></i>
                            Play Story
                        </a>
                    </div>
                </div>
            </div>

            <!-- Item Card 2 -->
            <div class="item-card animate-fade-in-up" style="animation-delay: 0.1s;">
                <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=200&fit=crop" alt="Concert Ticket" class="item-image">
                <div class="item-content">
                    <div class="item-header">
                        <div>
                            <div class="item-title">First Concert Ticket</div>
                            <div class="item-date">Added 1 week ago</div>
                        </div>
                        <div class="item-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="item-tags">
                        <span class="tag">Music</span>
                        <span class="tag">Youth</span>
                    </div>
                    
                    <div class="item-story">
                        My very first concert experience! The energy, the music, the crowd - everything was magical. This ticket stub represents the beginning of my love for live music...
                    </div>
                    
                    <div class="item-footer">
                        <a href="#" class="play-story">
                            <i class="fas fa-play"></i>
                            Play Story
                        </a>
                    </div>
                </div>
            </div>

            <!-- Item Card 3 -->
            <div class="item-card animate-fade-in-up" style="animation-delay: 0.2s;">
                <img src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=200&fit=crop" alt="Seashell" class="item-image">
                <div class="item-content">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Beach Memory Shell</div>
                            <div class="item-date">Added 2 weeks ago</div>
                        </div>
                        <div class="item-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="item-tags">
                        <span class="tag">Travel</span>
                        <span class="tag">Nature</span>
                    </div>
                    
                    <div class="item-story">
                        Found this perfect shell during my solo trip to the coast. It was a moment of pure peace, listening to the waves and feeling completely present...
                    </div>
                    
                    <div class="item-footer">
                        <a href="#" class="play-story">
                            <i class="fas fa-play"></i>
                            Play Story
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Add Button -->
    <div class="floating-add">
        <i class="fas fa-plus"></i>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="#" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-layer-group"></i>
            <span>Collections</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-plus-circle"></i>
            <span>Create</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-calendar-star"></i>
            <span>Memories</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-user-circle"></i>
            <span>Profile</span>
        </a>
    </div>
</body>
</html>
