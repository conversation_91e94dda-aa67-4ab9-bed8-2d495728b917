<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home - Item Story Keeper</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared.css">
    <style>
        .home-header {
            padding: 32px 24px;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(88, 86, 214, 0.05) 100%);
            position: relative;
            overflow: hidden;
        }

        .home-header::before {
            content: '';
            position: absolute;
            top: -60px;
            right: -60px;
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .home-header::after {
            content: '';
            position: absolute;
            bottom: -40px;
            left: -40px;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 209, 88, 0.1));
            border-radius: 50%;
            animation: float 8s ease-in-out infinite reverse;
        }

        .greeting {
            font-size: 32px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
            position: relative;
            z-index: 2;
        }

        .subtitle {
            font-size: 17px;
            color: #6e6e73;
            margin-bottom: 32px;
            letter-spacing: -0.022em;
            position: relative;
            z-index: 2;
        }

        .quick-stats {
            display: flex;
            gap: 12px;
            position: relative;
            z-index: 2;
        }

        .stat-item {
            flex: 1;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            padding: 20px 16px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .stat-item:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 16px 64px rgba(0, 0, 0, 0.12);
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #007aff, #5856d6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 4px;
            letter-spacing: -0.02em;
        }

        .stat-label {
            font-size: 13px;
            color: #6e6e73;
            font-weight: 500;
            letter-spacing: -0.08em;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 32px 24px 16px 24px;
        }

        .section-title {
            font-size: 22px;
            font-weight: 700;
            color: #1d1d1f;
            letter-spacing: -0.01em;
        }

        .see-all {
            font-size: 15px;
            color: #007aff;
            text-decoration: none;
            font-weight: 600;
            letter-spacing: -0.024em;
            padding: 8px 16px;
            border-radius: 12px;
            background: rgba(0, 122, 255, 0.1);
            transition: all 0.3s ease;
        }

        .see-all:hover {
            background: rgba(0, 122, 255, 0.2);
            transform: translateY(-1px);
        }

        .item-stream {
            padding: 0 24px 24px 24px;
        }

        .item-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border-radius: 28px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .item-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 64px rgba(0, 0, 0, 0.15);
        }

        .item-card::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 28px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 100%);
            pointer-events: none;
        }

        .item-image {
            width: 100%;
            height: 240px;
            object-fit: cover;
            background: linear-gradient(135deg, #f5f5f7, #e5e5ea);
            position: relative;
        }

        .item-image::after {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
        }

        .item-content {
            padding: 24px;
            position: relative;
            z-index: 2;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .item-title {
            font-size: 20px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 6px;
            letter-spacing: -0.005em;
        }

        .item-date {
            font-size: 13px;
            color: #8e8e93;
            font-weight: 500;
            letter-spacing: -0.08em;
        }

        .item-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            border: none;
            background: rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
            color: #6e6e73;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #007aff, #5856d6);
            color: white;
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
        }

        .item-story {
            font-size: 15px;
            color: #6e6e73;
            line-height: 1.47;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            letter-spacing: -0.024em;
        }

        .item-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }

        .tag {
            background: rgba(0, 122, 255, 0.1);
            color: #007aff;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: -0.08em;
            border: 1px solid rgba(0, 122, 255, 0.2);
        }

        .item-footer {
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .play-story {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            font-size: 15px;
            font-weight: 600;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 16px;
            background: linear-gradient(135deg, #34c759, #30d158);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 4px 16px rgba(52, 199, 89, 0.3);
            letter-spacing: -0.024em;
        }

        .play-story:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 32px rgba(52, 199, 89, 0.4);
        }

        .floating-add {
            position: fixed;
            bottom: 110px;
            right: 24px;
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #007aff, #5856d6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            box-shadow: 0 12px 40px rgba(0, 122, 255, 0.4);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 100;
            backdrop-filter: blur(20px);
        }

        .floating-add:hover {
            transform: scale(1.1) translateY(-4px);
            box-shadow: 0 20px 60px rgba(0, 122, 255, 0.5);
        }

        .floating-add:active {
            transform: scale(0.95);
        }

        .decorative-element {
            position: absolute;
            top: 24px;
            right: 24px;
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.3), rgba(88, 86, 214, 0.3));
            border-radius: 50%;
            animation: pulse 4s infinite;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-left">9:41</div>
        <div class="status-right">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <div class="battery-icon">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Home Header -->
        <div class="home-header">
            <div class="decorative-element"></div>
            <div class="greeting">Good morning, Sarah ✨</div>
            <div class="subtitle">Your memories are beautifully preserved</div>
            
            <div class="quick-stats">
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Items</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Collections</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">Anniversaries</div>
                </div>
            </div>
        </div>

        <!-- Recent Stories Section -->
        <div class="section-header">
            <div class="section-title">Recent Memories</div>
            <a href="#" class="see-all">View All</a>
        </div>

        <div class="item-stream">
            <!-- Item Card 1 -->
            <div class="item-card animate-fade-in-up">
                <img src="https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400&h=200&fit=crop" alt="Vintage Camera" class="item-image">
                <div class="item-content">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Grandpa's Camera</div>
                            <div class="item-date">Added 2 days ago</div>
                        </div>
                        <div class="item-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="item-tags">
                        <span class="tag">Family</span>
                        <span class="tag">Vintage</span>
                    </div>
                    
                    <div class="item-story">
                        This old camera belonged to my grandfather. He used it to capture countless family moments throughout the 1960s and 70s. Every click tells a story of love and memories...
                    </div>
                    
                    <div class="item-footer">
                        <a href="#" class="play-story">
                            <i class="fas fa-play"></i>
                            Play Story
                        </a>
                    </div>
                </div>
            </div>

            <!-- Item Card 2 -->
            <div class="item-card animate-fade-in-up" style="animation-delay: 0.1s;">
                <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=200&fit=crop" alt="Concert Ticket" class="item-image">
                <div class="item-content">
                    <div class="item-header">
                        <div>
                            <div class="item-title">First Concert Ticket</div>
                            <div class="item-date">Added 1 week ago</div>
                        </div>
                        <div class="item-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="item-tags">
                        <span class="tag">Music</span>
                        <span class="tag">Youth</span>
                    </div>
                    
                    <div class="item-story">
                        My very first concert experience! The energy, the music, the crowd - everything was magical. This ticket stub represents the beginning of my love for live music...
                    </div>
                    
                    <div class="item-footer">
                        <a href="#" class="play-story">
                            <i class="fas fa-play"></i>
                            Play Story
                        </a>
                    </div>
                </div>
            </div>

            <!-- Item Card 3 -->
            <div class="item-card animate-fade-in-up" style="animation-delay: 0.2s;">
                <img src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=200&fit=crop" alt="Seashell" class="item-image">
                <div class="item-content">
                    <div class="item-header">
                        <div>
                            <div class="item-title">Beach Memory Shell</div>
                            <div class="item-date">Added 2 weeks ago</div>
                        </div>
                        <div class="item-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="item-tags">
                        <span class="tag">Travel</span>
                        <span class="tag">Nature</span>
                    </div>
                    
                    <div class="item-story">
                        Found this perfect shell during my solo trip to the coast. It was a moment of pure peace, listening to the waves and feeling completely present...
                    </div>
                    
                    <div class="item-footer">
                        <a href="#" class="play-story">
                            <i class="fas fa-play"></i>
                            Play Story
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Add Button -->
    <div class="floating-add">
        <i class="fas fa-plus"></i>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="#" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-layer-group"></i>
            <span>Collections</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-plus-circle"></i>
            <span>Create</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-calendar-star"></i>
            <span>Memories</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-user-circle"></i>
            <span>Profile</span>
        </a>
    </div>
</body>
</html>
