<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collections - Item Story Keeper</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared.css">
    <style>
        .page-header {
            padding: 32px 24px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .page-title {
            font-size: 34px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
        }

        .page-subtitle {
            font-size: 17px;
            color: #6e6e73;
            letter-spacing: -0.022em;
        }

        .collections-grid {
            padding: 24px;
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
        }

        .collection-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border-radius: 28px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .collection-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 64px rgba(0, 0, 0, 0.15);
        }

        .collection-preview {
            height: 160px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .preview-items {
            display: grid;
            grid-template-columns: 1fr 1fr;
            height: 100%;
            gap: 2px;
        }

        .preview-item {
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .preview-item:nth-child(1) {
            grid-column: 1 / 2;
            grid-row: 1 / 3;
        }

        .preview-item:nth-child(2) {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
        }

        .preview-item:nth-child(3) {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
        }

        .preview-overlay {
            position: absolute;
            inset: 0;
            background: linear-gradient(45deg, rgba(255, 143, 163, 0.1), rgba(137, 207, 240, 0.1));
        }

        .item-count-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .celebration-badge {
            position: absolute;
            top: 12px;
            left: 12px;
            background: #FF8FA3;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            animation: bounce 2s infinite;
        }

        .collection-content {
            padding: 20px;
        }

        .collection-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .collection-title {
            font-size: 18px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 4px;
        }

        .collection-meta {
            font-size: 12px;
            color: #999999;
        }

        .collection-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            border: none;
            background: #F8F9FA;
            color: #666666;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #FF8FA3;
            color: white;
            transform: scale(1.1);
        }

        .collection-description {
            font-size: 14px;
            color: #666666;
            line-height: 1.4;
            margin-bottom: 16px;
        }

        .collection-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .play-all-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #89CFF0;
            color: white;
            padding: 8px 16px;
            border-radius: 12px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .play-all-btn:hover {
            background: #7BC5ED;
            transform: translateY(-1px);
        }

        .collection-stats {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #999999;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .create-collection {
            background: linear-gradient(135deg, rgba(255, 143, 163, 0.1), rgba(137, 207, 240, 0.1));
            border: 2px dashed #FF8FA3;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .create-collection:hover {
            background: linear-gradient(135deg, rgba(255, 143, 163, 0.2), rgba(137, 207, 240, 0.2));
            border-color: #FF7A96;
        }

        .create-icon {
            width: 48px;
            height: 48px;
            background: #FF8FA3;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-bottom: 12px;
        }

        .create-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 4px;
        }

        .create-subtitle {
            font-size: 14px;
            color: #666666;
        }

        .floating-add {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #FF8FA3, #FF7A96);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(255, 143, 163, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .floating-add:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(255, 143, 163, 0.5);
        }
    </style>
</head>
<body>
    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-left">9:41</div>
        <div class="status-right">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <div class="battery-icon">
                <div class="battery-fill"></div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-title">Story Collections</div>
            <div class="page-subtitle">Organize your memories by theme</div>
        </div>

        <!-- Collections Grid -->
        <div class="collections-grid">
            <!-- Collection 1 - Family Treasures -->
            <div class="collection-card animate-fade-in-up">
                <div class="collection-preview">
                    <div class="preview-items">
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=200&h=160&fit=crop')"></div>
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=200&h=80&fit=crop')"></div>
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=200&h=80&fit=crop')"></div>
                    </div>
                    <div class="preview-overlay"></div>
                    <div class="celebration-badge">🎉 5+ items!</div>
                    <div class="item-count-badge">7 items</div>
                </div>
                <div class="collection-content">
                    <div class="collection-header">
                        <div>
                            <div class="collection-title">Family Treasures</div>
                            <div class="collection-meta">Created 3 weeks ago</div>
                        </div>
                        <div class="collection-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="collection-description">
                        Precious items passed down through generations, each carrying the warmth of family love and cherished memories.
                    </div>
                    
                    <div class="collection-footer">
                        <a href="#" class="play-all-btn">
                            <i class="fas fa-play"></i>
                            Play All Stories
                        </a>
                        <div class="collection-stats">
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>12 min</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-calendar"></i>
                                <span>Last updated 2d ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Collection 2 - Travel Memories -->
            <div class="collection-card animate-fade-in-up" style="animation-delay: 0.1s;">
                <div class="collection-preview">
                    <div class="preview-items">
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=200&h=160&fit=crop')"></div>
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=80&fit=crop')"></div>
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=80&fit=crop')"></div>
                    </div>
                    <div class="preview-overlay"></div>
                    <div class="item-count-badge">3 items</div>
                </div>
                <div class="collection-content">
                    <div class="collection-header">
                        <div>
                            <div class="collection-title">Travel Memories</div>
                            <div class="collection-meta">Created 1 month ago</div>
                        </div>
                        <div class="collection-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="collection-description">
                        Souvenirs and mementos from adventures around the world, each telling a story of discovery and wonder.
                    </div>
                    
                    <div class="collection-footer">
                        <a href="#" class="play-all-btn">
                            <i class="fas fa-play"></i>
                            Play All Stories
                        </a>
                        <div class="collection-stats">
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>8 min</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-calendar"></i>
                                <span>Last updated 1w ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Collection 3 - Music & Concerts -->
            <div class="collection-card animate-fade-in-up" style="animation-delay: 0.2s;">
                <div class="collection-preview">
                    <div class="preview-items">
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=200&h=160&fit=crop')"></div>
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=200&h=80&fit=crop')"></div>
                        <div class="preview-item" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=200&h=80&fit=crop')"></div>
                    </div>
                    <div class="preview-overlay"></div>
                    <div class="item-count-badge">2 items</div>
                </div>
                <div class="collection-content">
                    <div class="collection-header">
                        <div>
                            <div class="collection-title">Music & Concerts</div>
                            <div class="collection-meta">Created 2 weeks ago</div>
                        </div>
                        <div class="collection-actions">
                            <button class="action-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="collection-description">
                        Concert tickets, vinyl records, and musical memories that soundtrack the most important moments of life.
                    </div>
                    
                    <div class="collection-footer">
                        <a href="#" class="play-all-btn">
                            <i class="fas fa-play"></i>
                            Play All Stories
                        </a>
                        <div class="collection-stats">
                            <div class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>5 min</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-calendar"></i>
                                <span>Last updated 3d ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create New Collection -->
            <div class="collection-card create-collection animate-fade-in-up" style="animation-delay: 0.3s;">
                <div class="create-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="create-title">Create New Collection</div>
                <div class="create-subtitle">Group your items by theme or story</div>
            </div>
        </div>
    </div>

    <!-- Floating Add Button -->
    <div class="floating-add">
        <i class="fas fa-plus"></i>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="#" class="nav-item">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="#" class="nav-item active">
            <i class="fas fa-layer-group"></i>
            <span>Collections</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-plus-circle"></i>
            <span>Create</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-calendar-star"></i>
            <span>Memories</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-user-circle"></i>
            <span>Profile</span>
        </a>
    </div>
</body>
</html>
