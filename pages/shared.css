/* Item Story Keeper - Modern Design System */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f5f5f7 0%, #fafafa 100%);
    color: #1d1d1f;
    width: 393px;
    height: 852px;
    overflow: hidden;
    position: relative;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* iPhone 15 Pro Status Bar */
.status-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 47px;
    background: rgba(245, 245, 247, 0.8);
    backdrop-filter: blur(40px) saturate(180%);
    -webkit-backdrop-filter: blur(40px) saturate(180%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-left {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
}

.battery-icon {
    width: 24px;
    height: 12px;
    border: 1px solid #333333;
    border-radius: 2px;
    position: relative;
}

.battery-icon::after {
    content: '';
    position: absolute;
    right: -3px;
    top: 3px;
    width: 2px;
    height: 6px;
    background: #333333;
    border-radius: 0 1px 1px 0;
}

.battery-fill {
    height: 100%;
    background: #4CAF50;
    border-radius: 1px;
    width: 80%;
}

/* Main Content Area */
.main-content {
    padding-top: 47px;
    padding-bottom: 83px;
    height: 100vh;
    overflow-y: auto;
}

/* Bottom Tab Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 83px;
    background: rgba(245, 245, 247, 0.8);
    backdrop-filter: blur(40px) saturate(180%);
    -webkit-backdrop-filter: blur(40px) saturate(180%);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    padding: 8px 0 34px 0;
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #8e8e93;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    padding: 4px 12px;
    border-radius: 12px;
    min-width: 60px;
    position: relative;
}

.nav-item.active {
    color: #007aff;
    background: rgba(0, 122, 255, 0.1);
    transform: translateY(-2px);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: #007aff;
    border-radius: 50%;
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item span {
    font-size: 10px;
    font-weight: 500;
}

/* Typography */
.h1 {
    font-size: 34px;
    font-weight: 700;
    color: #1d1d1f;
    line-height: 1.15;
    letter-spacing: -0.02em;
}

.h2 {
    font-size: 28px;
    font-weight: 600;
    color: #1d1d1f;
    line-height: 1.2;
    letter-spacing: -0.01em;
}

.h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1d1d1f;
    line-height: 1.3;
    letter-spacing: -0.005em;
}

.body-text {
    font-size: 17px;
    font-weight: 400;
    color: #1d1d1f;
    line-height: 1.47;
    letter-spacing: -0.022em;
}

.caption {
    font-size: 15px;
    font-weight: 400;
    color: #6e6e73;
    line-height: 1.4;
    letter-spacing: -0.024em;
}

.small-text {
    font-size: 13px;
    font-weight: 400;
    color: #8e8e93;
    line-height: 1.38;
    letter-spacing: -0.08em;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 28px;
    border-radius: 16px;
    font-size: 17px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-height: 50px;
    position: relative;
    letter-spacing: -0.022em;
}

.btn-primary {
    background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
    color: white;
    box-shadow: 0 8px 32px rgba(0, 122, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 122, 255, 0.4);
}

.btn-primary:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
    color: white;
    box-shadow: 0 8px 32px rgba(52, 199, 89, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 12px 40px rgba(52, 199, 89, 0.4);
}

.btn-outline {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    color: #007aff;
    border: 1px solid rgba(0, 122, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.btn-outline:hover {
    background: rgba(0, 122, 255, 0.1);
    border-color: rgba(0, 122, 255, 0.3);
    transform: translateY(-2px);
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 24px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.12);
}

.card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 24px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
}

/* Form Elements */
.input-group {
    margin-bottom: 24px;
}

.input-label {
    display: block;
    font-size: 15px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 12px;
    letter-spacing: -0.024em;
}

.input-field {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    font-size: 17px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    letter-spacing: -0.022em;
}

.input-field:focus {
    outline: none;
    border-color: #007aff;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    transform: translateY(-2px);
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-12px);
    }
    60% {
        transform: translateY(-6px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(32px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 8px rgba(0, 122, 255, 0.1);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-8px) rotate(2deg);
    }
}

.animate-bounce {
    animation: bounce 2s infinite;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-8 { margin-bottom: 8px; }
.mb-12 { margin-bottom: 12px; }
.mb-16 { margin-bottom: 16px; }
.mb-20 { margin-bottom: 20px; }
.mb-24 { margin-bottom: 24px; }

.mt-8 { margin-top: 8px; }
.mt-12 { margin-top: 12px; }
.mt-16 { margin-top: 16px; }
.mt-20 { margin-top: 20px; }
.mt-24 { margin-top: 24px; }

.p-16 { padding: 16px; }
.p-20 { padding: 20px; }
.p-24 { padding: 24px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded-8 { border-radius: 8px; }
.rounded-12 { border-radius: 12px; }
.rounded-16 { border-radius: 16px; }
.rounded-full { border-radius: 50%; }

.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow-md { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }

/* Color Classes */
.text-primary { color: #007aff; }
.text-secondary { color: #34c759; }
.text-accent { color: #5856d6; }
.text-gray { color: #6e6e73; }
.text-light-gray { color: #8e8e93; }

.bg-primary { background: linear-gradient(135deg, #007aff 0%, #5856d6 100%); }
.bg-secondary { background: linear-gradient(135deg, #34c759 0%, #30d158 100%); }
.bg-accent { background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%); }
.bg-glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}
.bg-dark-glass {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}
