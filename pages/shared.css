/* Item Story Keeper - Shared Design System */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #FFFDF7 0%, #FFF9F0 100%);
    color: #333333;
    width: 393px;
    height: 852px;
    overflow: hidden;
    position: relative;
}

/* iPhone 15 Pro Status Bar */
.status-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 47px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
    border-bottom: 1px solid rgba(239, 239, 239, 0.5);
}

.status-left {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
}

.battery-icon {
    width: 24px;
    height: 12px;
    border: 1px solid #333333;
    border-radius: 2px;
    position: relative;
}

.battery-icon::after {
    content: '';
    position: absolute;
    right: -3px;
    top: 3px;
    width: 2px;
    height: 6px;
    background: #333333;
    border-radius: 0 1px 1px 0;
}

.battery-fill {
    height: 100%;
    background: #4CAF50;
    border-radius: 1px;
    width: 80%;
}

/* Main Content Area */
.main-content {
    padding-top: 47px;
    padding-bottom: 83px;
    height: 100vh;
    overflow-y: auto;
}

/* Bottom Tab Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 83px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(239, 239, 239, 0.5);
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    padding: 8px 0 34px 0;
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #666666;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 8px;
    min-width: 60px;
}

.nav-item.active {
    color: #FF8FA3;
    background: rgba(255, 143, 163, 0.1);
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item span {
    font-size: 10px;
    font-weight: 500;
}

/* Typography */
.h1 {
    font-size: 32px;
    font-weight: 600;
    color: #333333;
    line-height: 1.2;
}

.h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333333;
    line-height: 1.3;
}

.h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    line-height: 1.4;
}

.body-text {
    font-size: 16px;
    font-weight: 400;
    color: #333333;
    line-height: 1.5;
}

.caption {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 1.4;
}

.small-text {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 1.3;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
    position: relative;
}

.btn-primary {
    background: #FF8FA3;
    color: white;
    box-shadow: 0 2px 4px rgba(255, 143, 163, 0.3);
}

.btn-primary:hover {
    background: #FF7A96;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 143, 163, 0.4);
}

.btn-primary:active {
    transform: scale(0.98);
    box-shadow: 0 1px 2px rgba(255, 143, 163, 0.3);
}

.btn-secondary {
    background: #89CFF0;
    color: white;
    box-shadow: 0 2px 4px rgba(137, 207, 240, 0.3);
}

.btn-secondary:hover {
    background: #7BC5ED;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(137, 207, 240, 0.4);
}

.btn-outline {
    background: transparent;
    color: #FF8FA3;
    border: 2px solid #FF8FA3;
}

.btn-outline:hover {
    background: #FF8FA3;
    color: white;
}

/* Cards */
.card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #EFEFEF;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    inset: 1px;
    border-radius: 15px;
    box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.5);
    pointer-events: none;
}

/* Form Elements */
.input-group {
    margin-bottom: 20px;
}

.input-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 8px;
}

.input-field {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #EFEFEF;
    border-radius: 12px;
    font-size: 16px;
    background: white;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: #FF8FA3;
    box-shadow: 0 0 0 3px rgba(255, 143, 163, 0.1);
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 2s infinite;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-8 { margin-bottom: 8px; }
.mb-12 { margin-bottom: 12px; }
.mb-16 { margin-bottom: 16px; }
.mb-20 { margin-bottom: 20px; }
.mb-24 { margin-bottom: 24px; }

.mt-8 { margin-top: 8px; }
.mt-12 { margin-top: 12px; }
.mt-16 { margin-top: 16px; }
.mt-20 { margin-top: 20px; }
.mt-24 { margin-top: 24px; }

.p-16 { padding: 16px; }
.p-20 { padding: 20px; }
.p-24 { padding: 24px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded-8 { border-radius: 8px; }
.rounded-12 { border-radius: 12px; }
.rounded-16 { border-radius: 16px; }
.rounded-full { border-radius: 50%; }

.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow-md { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }

/* Color Classes */
.text-primary { color: #FF8FA3; }
.text-secondary { color: #89CFF0; }
.text-gray { color: #666666; }
.text-light-gray { color: #999999; }

.bg-primary { background-color: #FF8FA3; }
.bg-secondary { background-color: #89CFF0; }
.bg-white { background-color: white; }
.bg-light { background-color: #F8F9FA; }
